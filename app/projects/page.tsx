import Projects from 'app/projects/projects';
import { Fragment } from 'react';
import Header from '../components/header';
import ScrollReveal from '../components/animations/scroll-reveal';
import FloatingElements from '../components/animations/floating-elements';
import ThemeSwitchWrapper from '../components/theme-switch-wrapper';

export const metadata = {
  title: 'Projects',
  description: 'My Projects - Viraj Shrivastav',
};

export default function Page() {
  return (
    <Fragment>
      <ThemeSwitchWrapper />
      <Header title="Projects" />
      <div className="relative">
        <FloatingElements count={6} className="opacity-20" />
        <div className="relative z-10">
          <ScrollReveal direction="up" delay={0.2}>
            <div className="space-y-2 md:space-y-5">
              <p className="text-lg leading-7 text-gray-500 dark:text-gray-400">
                Explore my collection of n8n automation workflows. Each project includes complete setup guides and ready-to-deploy JSON files for immediate implementation.
              </p>
            </div>
          </ScrollReveal>
          <ScrollReveal direction="up" delay={0.4}>
            <Projects />
          </ScrollReveal>
        </div>
      </div>
    </Fragment>
  );
}
