import Link from 'next/link';
import { ProjectModal } from './types';

interface ProjectProps {
  index: number;
  title: string;
  url?: string;
  slug?: string;
  role: string;
  setModal: (modal: ProjectModal) => void;
}

export default function ProjectItem({
  index,
  title,
  url,
  slug,
  role,
  setModal,
}: ProjectProps) {
  const href = slug ? `/projects/${slug}` : url || '#';
  const isExternal = !slug && url;

  return (
    <Link
      href={href}
      target={isExternal ? "_blank" : undefined}
      onMouseEnter={() => {
        setModal({ active: true, index });
      }}
      onMouseLeave={() => {
        setModal({ active: false, index });
      }}
      className="group flex w-full items-center justify-between border-b px-4 py-10 sm:px-10 sm:py-16"
      rel={isExternal ? "noreferrer" : undefined}
    >
      <h2 className="text-2xl transition-all group-hover:-translate-x-3 group-hover:scale-110 sm:text-4xl font-semibold">
        {title}
      </h2>
      <p className="text-sm font-light transition-all group-hover:translate-x-3 group-hover:scale-110 sm:text-base text-right">
        {role}
      </p>
    </Link>
  );
}
