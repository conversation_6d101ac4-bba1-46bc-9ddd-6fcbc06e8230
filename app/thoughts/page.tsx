import Header from '../components/header';
import PageContainer from '../components/layouts/page-container';
import { Thoughts } from '../components/thoughts';
import { getPosts } from './utils';
import ScrollReveal from '../components/animations/scroll-reveal';
import FloatingElements from '../components/animations/floating-elements';
import ThemeSwitchWrapper from '../components/theme-switch-wrapper';
import { Fragment } from 'react';

export const metadata = {
  title: 'Thoughts',
  description: 'My Thoughts - Viraj Shrivastav',
};

export default function ThoughtsPage() {
  const posts = getPosts();

  return (
    <Fragment>
      <ThemeSwitchWrapper />
      <PageContainer>
        <Header title="Thoughts" />
        <div className="relative">
          <FloatingElements count={5} className="opacity-20" />
          <div className="relative z-10">
            <ScrollReveal direction="up" delay={0.2}>
              <div className="space-y-2 md:space-y-5 mb-8">
                <p className="text-lg leading-7 text-gray-500 dark:text-gray-400">
                  Sharing a few insights and opinions about the tech and AI world—take them with a grain of salt.
                </p>
              </div>
            </ScrollReveal>
            <ScrollReveal direction="up" delay={0.4}>
              <Thoughts posts={posts} />
            </ScrollReveal>
          </div>
        </div>
      </PageContainer>
    </Fragment>
  );
}
